package com.implatform.admin.controller;

import com.implatform.admin.clients.MessageServiceWebClient;
import com.implatform.common.core.domain.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 消息管理Controller
 * 代理消息服务的所有管理接口
 */
@Tag(name = "消息管理", description = "消息服务管理接口")
@RestController
@RequestMapping("/api/v1/admin/messages")
@RequiredArgsConstructor
public class MessageManagementController {

    private final MessageServiceWebClient messageServiceClient;

    // ==================== 消息管理 ====================
    
    @Operation(summary = "获取消息列表")
    @GetMapping
    public Result<Page<Object>> getMessages(@RequestParam Map<String, Object> params, Pageable pageable) {
        return messageServiceClient.getMessages(params, pageable);
    }
    
    @Operation(summary = "根据ID获取消息")
    @GetMapping("/{id}")
    public Result<Object> getMessageById(@PathVariable Long id) {
        return messageServiceClient.getMessageById(id);
    }
    
    @Operation(summary = "删除消息")
    @DeleteMapping("/{id}")
    public Result<Void> deleteMessage(@PathVariable Long id) {
        return messageServiceClient.deleteMessage(id);
    }
    
    @Operation(summary = "批量删除消息")
    @DeleteMapping("/batch")
    public Result<Void> deleteMessages(@RequestBody List<Long> ids) {
        return messageServiceClient.deleteMessages(ids);
    }
    
    @Operation(summary = "撤回消息")
    @PostMapping("/{id}/recall")
    public Result<Void> recallMessage(@PathVariable Long id) {
        return messageServiceClient.recallMessage(id);
    }

    // ==================== 搜索索引管理 ====================
    
    @Operation(summary = "获取搜索索引列表")
    @GetMapping("/search-index")
    public Result<Page<Object>> getSearchIndex(@RequestParam Map<String, Object> params, Pageable pageable) {
        return messageServiceClient.getSearchIndex(params, pageable);
    }
    
    @Operation(summary = "根据ID获取搜索索引")
    @GetMapping("/search-index/{id}")
    public Result<Object> getSearchIndexById(@PathVariable Long id) {
        return messageServiceClient.getSearchIndexById(id);
    }
    
    @Operation(summary = "重建搜索索引")
    @PostMapping("/search-index/rebuild")
    public Result<Void> rebuildSearchIndex() {
        return messageServiceClient.rebuildSearchIndex();
    }
    
    @Operation(summary = "删除搜索索引")
    @DeleteMapping("/search-index/{id}")
    public Result<Void> deleteSearchIndex(@PathVariable Long id) {
        return messageServiceClient.deleteSearchIndex(id);
    }

    // ==================== 消息反应管理 ====================
    
    @Operation(summary = "获取消息反应列表")
    @GetMapping("/reactions")
    public Result<Page<Object>> getReactions(@RequestParam Map<String, Object> params, Pageable pageable) {
        return messageServiceClient.getReactions(params, pageable);
    }
    
    @Operation(summary = "根据ID获取消息反应")
    @GetMapping("/reactions/{id}")
    public Result<Object> getReactionById(@PathVariable Long id) {
        return messageServiceClient.getReactionById(id);
    }
    
    @Operation(summary = "删除消息反应")
    @DeleteMapping("/reactions/{id}")
    public Result<Void> deleteReaction(@PathVariable Long id) {
        return messageServiceClient.deleteReaction(id);
    }

    // ==================== 消息编辑历史管理 ====================
    
    @Operation(summary = "获取消息编辑历史列表")
    @GetMapping("/edit-history")
    public Result<Page<Object>> getEditHistory(@RequestParam Map<String, Object> params, Pageable pageable) {
        return messageServiceClient.getEditHistory(params, pageable);
    }
    
    @Operation(summary = "根据ID获取消息编辑历史")
    @GetMapping("/edit-history/{id}")
    public Result<Object> getEditHistoryById(@PathVariable Long id) {
        return messageServiceClient.getEditHistoryById(id);
    }

    // ==================== 消息草稿管理 ====================
    
    @Operation(summary = "获取消息草稿列表")
    @GetMapping("/drafts")
    public Result<Page<Object>> getDrafts(@RequestParam Map<String, Object> params, Pageable pageable) {
        return messageServiceClient.getDrafts(params, pageable);
    }
    
    @Operation(summary = "根据ID获取消息草稿")
    @GetMapping("/drafts/{id}")
    public Result<Object> getDraftById(@PathVariable Long id) {
        return messageServiceClient.getDraftById(id);
    }
    
    @Operation(summary = "删除消息草稿")
    @DeleteMapping("/drafts/{id}")
    public Result<Void> deleteDraft(@PathVariable Long id) {
        return messageServiceClient.deleteDraft(id);
    }

    // ==================== 定时消息管理 ====================
    
    @Operation(summary = "获取定时消息列表")
    @GetMapping("/scheduled")
    public Result<Page<Object>> getScheduledMessages(@RequestParam Map<String, Object> params, Pageable pageable) {
        return messageServiceClient.getScheduledMessages(params, pageable);
    }
    
    @Operation(summary = "根据ID获取定时消息")
    @GetMapping("/scheduled/{id}")
    public Result<Object> getScheduledMessageById(@PathVariable Long id) {
        return messageServiceClient.getScheduledMessageById(id);
    }
    
    @Operation(summary = "取消定时消息")
    @PostMapping("/scheduled/{id}/cancel")
    public Result<Void> cancelScheduledMessage(@PathVariable Long id) {
        return messageServiceClient.cancelScheduledMessage(id);
    }
    
    @Operation(summary = "删除定时消息")
    @DeleteMapping("/scheduled/{id}")
    public Result<Void> deleteScheduledMessage(@PathVariable Long id) {
        return messageServiceClient.deleteScheduledMessage(id);
    }

    // ==================== 置顶消息管理 ====================
    
    @Operation(summary = "获取置顶消息列表")
    @GetMapping("/pinned")
    public Result<Page<Object>> getPinnedMessages(@RequestParam Map<String, Object> params, Pageable pageable) {
        return messageServiceClient.getPinnedMessages(params, pageable);
    }
    
    @Operation(summary = "根据ID获取置顶消息")
    @GetMapping("/pinned/{id}")
    public Result<Object> getPinnedMessageById(@PathVariable Long id) {
        return messageServiceClient.getPinnedMessageById(id);
    }
    
    @Operation(summary = "取消置顶消息")
    @PostMapping("/pinned/{id}/unpin")
    public Result<Void> unpinMessage(@PathVariable Long id) {
        return messageServiceClient.unpinMessage(id);
    }
    
    @Operation(summary = "删除置顶消息")
    @DeleteMapping("/pinned/{id}")
    public Result<Void> deletePinnedMessage(@PathVariable Long id) {
        return messageServiceClient.deletePinnedMessage(id);
    }

    // ==================== 加密消息管理 ====================
    
    @Operation(summary = "获取加密消息列表")
    @GetMapping("/encrypted")
    public Result<Page<Object>> getEncryptedMessages(@RequestParam Map<String, Object> params, Pageable pageable) {
        return messageServiceClient.getEncryptedMessages(params, pageable);
    }
    
    @Operation(summary = "根据ID获取加密消息")
    @GetMapping("/encrypted/{id}")
    public Result<Object> getEncryptedMessageById(@PathVariable Long id) {
        return messageServiceClient.getEncryptedMessageById(id);
    }

    // ==================== 频道消息管理 ====================
    
    @Operation(summary = "获取频道消息列表")
    @GetMapping("/channel")
    public Result<Page<Object>> getChannelMessages(@RequestParam Map<String, Object> params, Pageable pageable) {
        return messageServiceClient.getChannelMessages(params, pageable);
    }
    
    @Operation(summary = "根据ID获取频道消息")
    @GetMapping("/channel/{id}")
    public Result<Object> getChannelMessageById(@PathVariable Long id) {
        return messageServiceClient.getChannelMessageById(id);
    }
    
    @Operation(summary = "删除频道消息")
    @DeleteMapping("/channel/{id}")
    public Result<Void> deleteChannelMessage(@PathVariable Long id) {
        return messageServiceClient.deleteChannelMessage(id);
    }

    // ==================== 广播消息管理 ====================
    
    @Operation(summary = "获取广播消息列表")
    @GetMapping("/broadcast")
    public Result<Page<Object>> getBroadcastMessages(@RequestParam Map<String, Object> params, Pageable pageable) {
        return messageServiceClient.getBroadcastMessages(params, pageable);
    }
    
    @Operation(summary = "根据ID获取广播消息")
    @GetMapping("/broadcast/{id}")
    public Result<Object> getBroadcastMessageById(@PathVariable Long id) {
        return messageServiceClient.getBroadcastMessageById(id);
    }
    
    @Operation(summary = "创建广播消息")
    @PostMapping("/broadcast")
    public Result<Object> createBroadcastMessage(@RequestBody Object request) {
        return messageServiceClient.createBroadcastMessage(request);
    }
    
    @Operation(summary = "删除广播消息")
    @DeleteMapping("/broadcast/{id}")
    public Result<Void> deleteBroadcastMessage(@PathVariable Long id) {
        return messageServiceClient.deleteBroadcastMessage(id);
    }

    // ==================== 广播接收者管理 ====================
    
    @Operation(summary = "获取广播接收者列表")
    @GetMapping("/broadcast-recipients")
    public Result<Page<Object>> getBroadcastRecipients(@RequestParam Map<String, Object> params, Pageable pageable) {
        return messageServiceClient.getBroadcastRecipients(params, pageable);
    }
    
    @Operation(summary = "根据ID获取广播接收者")
    @GetMapping("/broadcast-recipients/{id}")
    public Result<Object> getBroadcastRecipientById(@PathVariable Long id) {
        return messageServiceClient.getBroadcastRecipientById(id);
    }

    // ==================== 广播任务管理 ====================
    
    @Operation(summary = "获取广播任务列表")
    @GetMapping("/broadcast-tasks")
    public Result<Page<Object>> getBroadcastTasks(@RequestParam Map<String, Object> params, Pageable pageable) {
        return messageServiceClient.getBroadcastTasks(params, pageable);
    }
    
    @Operation(summary = "根据ID获取广播任务")
    @GetMapping("/broadcast-tasks/{id}")
    public Result<Object> getBroadcastTaskById(@PathVariable Long id) {
        return messageServiceClient.getBroadcastTaskById(id);
    }
    
    @Operation(summary = "取消广播任务")
    @PostMapping("/broadcast-tasks/{id}/cancel")
    public Result<Void> cancelBroadcastTask(@PathVariable Long id) {
        return messageServiceClient.cancelBroadcastTask(id);
    }

    // ==================== 搜索查询管理 ====================
    
    @Operation(summary = "获取搜索查询列表")
    @GetMapping("/search-queries")
    public Result<Page<Object>> getSearchQueries(@RequestParam Map<String, Object> params, Pageable pageable) {
        return messageServiceClient.getSearchQueries(params, pageable);
    }
    
    @Operation(summary = "根据ID获取搜索查询")
    @GetMapping("/search-queries/{id}")
    public Result<Object> getSearchQueryById(@PathVariable Long id) {
        return messageServiceClient.getSearchQueryById(id);
    }

    // ==================== 搜索历史管理 ====================
    
    @Operation(summary = "获取搜索历史列表")
    @GetMapping("/search-history")
    public Result<Page<Object>> getSearchHistory(@RequestParam Map<String, Object> params, Pageable pageable) {
        return messageServiceClient.getSearchHistory(params, pageable);
    }
    
    @Operation(summary = "根据ID获取搜索历史")
    @GetMapping("/search-history/{id}")
    public Result<Object> getSearchHistoryById(@PathVariable Long id) {
        return messageServiceClient.getSearchHistoryById(id);
    }
    
    @Operation(summary = "删除搜索历史")
    @DeleteMapping("/search-history/{id}")
    public Result<Void> deleteSearchHistory(@PathVariable Long id) {
        return messageServiceClient.deleteSearchHistory(id);
    }

    // ==================== 搜索过滤器管理 ====================
    
    @Operation(summary = "获取搜索过滤器列表")
    @GetMapping("/search-filters")
    public Result<Page<Object>> getSearchFilters(@RequestParam Map<String, Object> params, Pageable pageable) {
        return messageServiceClient.getSearchFilters(params, pageable);
    }
    
    @Operation(summary = "根据ID获取搜索过滤器")
    @GetMapping("/search-filters/{id}")
    public Result<Object> getSearchFilterById(@PathVariable Long id) {
        return messageServiceClient.getSearchFilterById(id);
    }
    
    @Operation(summary = "创建搜索过滤器")
    @PostMapping("/search-filters")
    public Result<Object> createSearchFilter(@RequestBody Object request) {
        return messageServiceClient.createSearchFilter(request);
    }
    
    @Operation(summary = "更新搜索过滤器")
    @PutMapping("/search-filters/{id}")
    public Result<Object> updateSearchFilter(@PathVariable Long id, @RequestBody Object request) {
        return messageServiceClient.updateSearchFilter(id, request);
    }
    
    @Operation(summary = "删除搜索过滤器")
    @DeleteMapping("/search-filters/{id}")
    public Result<Void> deleteSearchFilter(@PathVariable Long id) {
        return messageServiceClient.deleteSearchFilter(id);
    }

    // ==================== 敏感词管理 ====================
    
    @Operation(summary = "获取敏感词列表")
    @GetMapping("/sensitive-words")
    public Result<Page<Object>> getSensitiveWords(@RequestParam Map<String, Object> params, Pageable pageable) {
        return messageServiceClient.getSensitiveWords(params, pageable);
    }
    
    @Operation(summary = "根据ID获取敏感词")
    @GetMapping("/sensitive-words/{id}")
    public Result<Object> getSensitiveWordById(@PathVariable Long id) {
        return messageServiceClient.getSensitiveWordById(id);
    }
    
    @Operation(summary = "创建敏感词")
    @PostMapping("/sensitive-words")
    public Result<Object> createSensitiveWord(@RequestBody Object request) {
        return messageServiceClient.createSensitiveWord(request);
    }
    
    @Operation(summary = "更新敏感词")
    @PutMapping("/sensitive-words/{id}")
    public Result<Object> updateSensitiveWord(@PathVariable Long id, @RequestBody Object request) {
        return messageServiceClient.updateSensitiveWord(id, request);
    }
    
    @Operation(summary = "删除敏感词")
    @DeleteMapping("/sensitive-words/{id}")
    public Result<Void> deleteSensitiveWord(@PathVariable Long id) {
        return messageServiceClient.deleteSensitiveWord(id);
    }
    
    @Operation(summary = "批量导入敏感词")
    @PostMapping("/sensitive-words/batch-import")
    public Result<Void> batchImportSensitiveWords(@RequestBody List<Object> words) {
        return messageServiceClient.batchImportSensitiveWords(words);
    }

    // ==================== 敏感词检测管理 ====================
    
    @Operation(summary = "获取敏感词检测列表")
    @GetMapping("/sensitive-detection")
    public Result<Page<Object>> getSensitiveDetection(@RequestParam Map<String, Object> params, Pageable pageable) {
        return messageServiceClient.getSensitiveDetection(params, pageable);
    }
    
    @Operation(summary = "根据ID获取敏感词检测")
    @GetMapping("/sensitive-detection/{id}")
    public Result<Object> getSensitiveDetectionById(@PathVariable Long id) {
        return messageServiceClient.getSensitiveDetectionById(id);
    }
    
    @Operation(summary = "处理敏感词检测")
    @PostMapping("/sensitive-detection/{id}/handle")
    public Result<Void> handleSensitiveDetection(@PathVariable Long id, @RequestBody Object request) {
        return messageServiceClient.handleSensitiveDetection(id, request);
    }
}
