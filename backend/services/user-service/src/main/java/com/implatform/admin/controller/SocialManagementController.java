package com.implatform.admin.controller;

import com.implatform.admin.clients.SocialServiceWebClient;
import com.implatform.common.core.domain.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 社交管理Controller
 * 代理社交服务的所有管理接口
 */
@Tag(name = "社交管理", description = "社交服务管理接口")
@RestController
@RequestMapping("/api/v1/admin/social")
@RequiredArgsConstructor
public class SocialManagementController {

    private final SocialServiceWebClient socialServiceClient;

    // ==================== 群组管理 ====================
    
    @Operation(summary = "获取群组列表")
    @GetMapping("/groups")
    public Result<Page<Object>> getGroups(@RequestParam Map<String, Object> params, Pageable pageable) {
        return socialServiceClient.getGroups(params, pageable);
    }
    
    @Operation(summary = "根据ID获取群组")
    @GetMapping("/groups/{id}")
    public Result<Object> getGroupById(@PathVariable Long id) {
        return socialServiceClient.getGroupById(id);
    }
    
    @Operation(summary = "创建群组")
    @PostMapping("/groups")
    public Result<Object> createGroup(@RequestBody Object request) {
        return socialServiceClient.createGroup(request);
    }
    
    @Operation(summary = "更新群组")
    @PutMapping("/groups/{id}")
    public Result<Object> updateGroup(@PathVariable Long id, @RequestBody Object request) {
        return socialServiceClient.updateGroup(id, request);
    }
    
    @Operation(summary = "删除群组")
    @DeleteMapping("/groups/{id}")
    public Result<Void> deleteGroup(@PathVariable Long id) {
        return socialServiceClient.deleteGroup(id);
    }
    
    @Operation(summary = "解散群组")
    @PostMapping("/groups/{id}/dissolve")
    public Result<Void> dissolveGroup(@PathVariable Long id) {
        return socialServiceClient.dissolveGroup(id);
    }

    // ==================== 群组成员管理 ====================
    
    @Operation(summary = "获取群组成员列表")
    @GetMapping("/group-members")
    public Result<Page<Object>> getGroupMembers(@RequestParam Map<String, Object> params, Pageable pageable) {
        return socialServiceClient.getGroupMembers(params, pageable);
    }
    
    @Operation(summary = "根据ID获取群组成员")
    @GetMapping("/group-members/{id}")
    public Result<Object> getGroupMemberById(@PathVariable Long id) {
        return socialServiceClient.getGroupMemberById(id);
    }
    
    @Operation(summary = "移除群组成员")
    @DeleteMapping("/group-members/{id}")
    public Result<Void> removeGroupMember(@PathVariable Long id) {
        return socialServiceClient.removeGroupMember(id);
    }

    // ==================== 机器人管理 ====================
    
    @Operation(summary = "获取机器人列表")
    @GetMapping("/bots")
    public Result<Page<Object>> getBots(@RequestParam Map<String, Object> params, Pageable pageable) {
        return socialServiceClient.getBots(params, pageable);
    }
    
    @Operation(summary = "根据ID获取机器人")
    @GetMapping("/bots/{id}")
    public Result<Object> getBotById(@PathVariable Long id) {
        return socialServiceClient.getBotById(id);
    }
    
    @Operation(summary = "创建机器人")
    @PostMapping("/bots")
    public Result<Object> createBot(@RequestBody Object request) {
        return socialServiceClient.createBot(request);
    }
    
    @Operation(summary = "更新机器人")
    @PutMapping("/bots/{id}")
    public Result<Object> updateBot(@PathVariable Long id, @RequestBody Object request) {
        return socialServiceClient.updateBot(id, request);
    }
    
    @Operation(summary = "删除机器人")
    @DeleteMapping("/bots/{id}")
    public Result<Void> deleteBot(@PathVariable Long id) {
        return socialServiceClient.deleteBot(id);
    }
    
    @Operation(summary = "启用/禁用机器人")
    @PostMapping("/bots/{id}/toggle")
    public Result<Void> toggleBot(@PathVariable Long id) {
        return socialServiceClient.toggleBot(id);
    }

    // ==================== 频道管理 ====================
    
    @Operation(summary = "获取频道列表")
    @GetMapping("/channels")
    public Result<Page<Object>> getChannels(@RequestParam Map<String, Object> params, Pageable pageable) {
        return socialServiceClient.getChannels(params, pageable);
    }
    
    @Operation(summary = "根据ID获取频道")
    @GetMapping("/channels/{id}")
    public Result<Object> getChannelById(@PathVariable Long id) {
        return socialServiceClient.getChannelById(id);
    }
    
    @Operation(summary = "创建频道")
    @PostMapping("/channels")
    public Result<Object> createChannel(@RequestBody Object request) {
        return socialServiceClient.createChannel(request);
    }
    
    @Operation(summary = "更新频道")
    @PutMapping("/channels/{id}")
    public Result<Object> updateChannel(@PathVariable Long id, @RequestBody Object request) {
        return socialServiceClient.updateChannel(id, request);
    }
    
    @Operation(summary = "删除频道")
    @DeleteMapping("/channels/{id}")
    public Result<Void> deleteChannel(@PathVariable Long id) {
        return socialServiceClient.deleteChannel(id);
    }

    // ==================== AI助手管理 ====================
    
    @Operation(summary = "获取AI助手列表")
    @GetMapping("/ai-assistants")
    public Result<Page<Object>> getAiAssistants(@RequestParam Map<String, Object> params, Pageable pageable) {
        return socialServiceClient.getAiAssistants(params, pageable);
    }
    
    @Operation(summary = "根据ID获取AI助手")
    @GetMapping("/ai-assistants/{id}")
    public Result<Object> getAiAssistantById(@PathVariable Long id) {
        return socialServiceClient.getAiAssistantById(id);
    }
    
    @Operation(summary = "创建AI助手")
    @PostMapping("/ai-assistants")
    public Result<Object> createAiAssistant(@RequestBody Object request) {
        return socialServiceClient.createAiAssistant(request);
    }
    
    @Operation(summary = "更新AI助手")
    @PutMapping("/ai-assistants/{id}")
    public Result<Object> updateAiAssistant(@PathVariable Long id, @RequestBody Object request) {
        return socialServiceClient.updateAiAssistant(id, request);
    }
    
    @Operation(summary = "删除AI助手")
    @DeleteMapping("/ai-assistants/{id}")
    public Result<Void> deleteAiAssistant(@PathVariable Long id) {
        return socialServiceClient.deleteAiAssistant(id);
    }

    // ==================== 投票管理 ====================
    
    @Operation(summary = "获取投票列表")
    @GetMapping("/polls")
    public Result<Page<Object>> getPolls(@RequestParam Map<String, Object> params, Pageable pageable) {
        return socialServiceClient.getPolls(params, pageable);
    }
    
    @Operation(summary = "根据ID获取投票")
    @GetMapping("/polls/{id}")
    public Result<Object> getPollById(@PathVariable Long id) {
        return socialServiceClient.getPollById(id);
    }
    
    @Operation(summary = "创建投票")
    @PostMapping("/polls")
    public Result<Object> createPoll(@RequestBody Object request) {
        return socialServiceClient.createPoll(request);
    }
    
    @Operation(summary = "更新投票")
    @PutMapping("/polls/{id}")
    public Result<Object> updatePoll(@PathVariable Long id, @RequestBody Object request) {
        return socialServiceClient.updatePoll(id, request);
    }
    
    @Operation(summary = "删除投票")
    @DeleteMapping("/polls/{id}")
    public Result<Void> deletePoll(@PathVariable Long id) {
        return socialServiceClient.deletePoll(id);
    }
    
    @Operation(summary = "结束投票")
    @PostMapping("/polls/{id}/end")
    public Result<Void> endPoll(@PathVariable Long id) {
        return socialServiceClient.endPoll(id);
    }

    // ==================== 官方账号管理 ====================
    
    @Operation(summary = "获取官方账号列表")
    @GetMapping("/official-accounts")
    public Result<Page<Object>> getOfficialAccounts(@RequestParam Map<String, Object> params, Pageable pageable) {
        return socialServiceClient.getOfficialAccounts(params, pageable);
    }
    
    @Operation(summary = "根据ID获取官方账号")
    @GetMapping("/official-accounts/{id}")
    public Result<Object> getOfficialAccountById(@PathVariable Long id) {
        return socialServiceClient.getOfficialAccountById(id);
    }
    
    @Operation(summary = "创建官方账号")
    @PostMapping("/official-accounts")
    public Result<Object> createOfficialAccount(@RequestBody Object request) {
        return socialServiceClient.createOfficialAccount(request);
    }
    
    @Operation(summary = "更新官方账号")
    @PutMapping("/official-accounts/{id}")
    public Result<Object> updateOfficialAccount(@PathVariable Long id, @RequestBody Object request) {
        return socialServiceClient.updateOfficialAccount(id, request);
    }
    
    @Operation(summary = "删除官方账号")
    @DeleteMapping("/official-accounts/{id}")
    public Result<Void> deleteOfficialAccount(@PathVariable Long id) {
        return socialServiceClient.deleteOfficialAccount(id);
    }

    // ==================== 短视频管理 ====================
    
    @Operation(summary = "获取短视频列表")
    @GetMapping("/short-videos")
    public Result<Page<Object>> getShortVideos(@RequestParam Map<String, Object> params, Pageable pageable) {
        return socialServiceClient.getShortVideos(params, pageable);
    }
    
    @Operation(summary = "根据ID获取短视频")
    @GetMapping("/short-videos/{id}")
    public Result<Object> getShortVideoById(@PathVariable Long id) {
        return socialServiceClient.getShortVideoById(id);
    }
    
    @Operation(summary = "删除短视频")
    @DeleteMapping("/short-videos/{id}")
    public Result<Void> deleteShortVideo(@PathVariable Long id) {
        return socialServiceClient.deleteShortVideo(id);
    }
    
    @Operation(summary = "审核短视频")
    @PostMapping("/short-videos/{id}/review")
    public Result<Void> reviewShortVideo(@PathVariable Long id, @RequestBody Object request) {
        return socialServiceClient.reviewShortVideo(id, request);
    }

    // ==================== 积分管理 ====================
    
    @Operation(summary = "获取积分账户列表")
    @GetMapping("/coin-accounts")
    public Result<Page<Object>> getCoinAccounts(@RequestParam Map<String, Object> params, Pageable pageable) {
        return socialServiceClient.getCoinAccounts(params, pageable);
    }
    
    @Operation(summary = "根据ID获取积分账户")
    @GetMapping("/coin-accounts/{id}")
    public Result<Object> getCoinAccountById(@PathVariable Long id) {
        return socialServiceClient.getCoinAccountById(id);
    }
    
    @Operation(summary = "调整积分余额")
    @PostMapping("/coin-accounts/{id}/adjust")
    public Result<Void> adjustCoinBalance(@PathVariable Long id, @RequestBody Object request) {
        return socialServiceClient.adjustCoinBalance(id, request);
    }

    @Operation(summary = "获取积分交易列表")
    @GetMapping("/coin-transactions")
    public Result<Page<Object>> getCoinTransactions(@RequestParam Map<String, Object> params, Pageable pageable) {
        return socialServiceClient.getCoinTransactions(params, pageable);
    }

    @Operation(summary = "获取积分规则列表")
    @GetMapping("/coin-rules")
    public Result<Page<Object>> getCoinRules(@RequestParam Map<String, Object> params, Pageable pageable) {
        return socialServiceClient.getCoinRules(params, pageable);
    }
    
    @Operation(summary = "创建积分规则")
    @PostMapping("/coin-rules")
    public Result<Object> createCoinRule(@RequestBody Object request) {
        return socialServiceClient.createCoinRule(request);
    }
    
    @Operation(summary = "更新积分规则")
    @PutMapping("/coin-rules/{id}")
    public Result<Object> updateCoinRule(@PathVariable Long id, @RequestBody Object request) {
        return socialServiceClient.updateCoinRule(id, request);
    }
    
    @Operation(summary = "删除积分规则")
    @DeleteMapping("/coin-rules/{id}")
    public Result<Void> deleteCoinRule(@PathVariable Long id) {
        return socialServiceClient.deleteCoinRule(id);
    }
}
